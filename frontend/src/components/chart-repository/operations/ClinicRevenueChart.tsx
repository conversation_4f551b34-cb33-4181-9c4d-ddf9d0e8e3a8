import React from "react"
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"

import { formatAbbreviatedCurrency } from "@/lib/number"
import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample data for clinic revenue metrics
const clinicRevenueData = [
  {
    segment: "Primary Care",
    revenuePerClinic: 1250000,
    totalRevenue: 52500000,
    targetRevenue: 1300000,
  },
  {
    segment: "Specialty Care",
    revenuePerClinic: 1850000,
    totalRevenue: 51800000,
    targetRevenue: 1800000,
  },
  {
    segment: "Urgent Care",
    revenuePerClinic: 980000,
    totalRevenue: 14700000,
    targetRevenue: 1000000,
  },
  {
    segment: "Pediatrics",
    revenuePerClinic: 920000,
    totalRevenue: 11040000,
    targetRevenue: 950000,
  },
  {
    segment: "Women's Health",
    revenuePerClinic: 1420000,
    totalRevenue: 11360000,
    targetRevenue: 1400000,
  },
  {
    segment: "Mental Health",
    revenuePerClinic: 850000,
    totalRevenue: 5100000,
    targetRevenue: 900000,
  },
  {
    segment: "Rehabilitation",
    revenuePerClinic: 780000,
    totalRevenue: 3900000,
    targetRevenue: 850000,
  },
]

const ClinicRevenueChart = ({
  data = clinicRevenueData,
}: {
  data?: typeof clinicRevenueData
}) => {
  // Ensure data is an array
  const chartData = Array.isArray(data) ? data : clinicRevenueData

  // Calculate average revenue per clinic
  const avgRevenuePerClinic = Math.round(
    chartData.reduce(
      (sum: number, item: any) => sum + item.revenuePerClinic,
      0
    ) / chartData.length
  )

  // Calculate total revenue across all clinics
  const totalRevenue = chartData.reduce(
    (sum: number, item: any) => sum + item.totalRevenue,
    0
  )

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <div className="rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
            Total Revenue: {formatAbbreviatedCurrency(totalRevenue, 0)}
          </div>
          <div className="rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
            Avg Per Clinic: {formatAbbreviatedCurrency(avgRevenuePerClinic, 0)}
          </div>
        </div>
      </div>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <ComposedChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="segment" tick={{ fontSize: 12 }} />
          <YAxis
            yAxisId="left"
            orientation="left"
            tickFormatter={(value) => formatAbbreviatedCurrency(value, 0)}
            tick={{ fontSize: 12 }}
          />
          <YAxis
            yAxisId="right"
            orientation="right"
            tickFormatter={(value) => formatAbbreviatedCurrency(value, 0)}
            tick={{ fontSize: 12 }}
          />
          <Tooltip
            formatter={(value, name) => [
              formatAbbreviatedCurrency(value as number, 0),
              name,
            ]}
          />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Bar
            dataKey="revenuePerClinic"
            name="Revenue Per Clinic"
            fill="#8884d8"
            yAxisId="left"
          />
          <Line
            type="monotone"
            dataKey="targetRevenue"
            name="Target Revenue"
            stroke="#ff7300"
            yAxisId="left"
            strokeWidth={2}
          />
          <ReferenceLine
            y={avgRevenuePerClinic}
            yAxisId="left"
            stroke="#82ca9d"
            strokeDasharray="3 3"
            label={{ value: "Avg", position: "insideBottomLeft", fontSize: 12 }}
          />
        </ComposedChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-2">
        <div className="rounded bg-blue-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">
            Avg Revenue Per Clinic
          </p>
          <p className="text-lg font-bold text-blue-600">
            {formatAbbreviatedCurrency(avgRevenuePerClinic, 0)}
          </p>
        </div>
        <div className="rounded bg-green-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">Total Revenue</p>
          <p className="text-lg font-bold text-green-600">
            {formatAbbreviatedCurrency(totalRevenue, 0)}
          </p>
        </div>
        <div className="rounded bg-purple-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">
            Highest Performing
          </p>
          <p className="text-lg font-bold text-purple-600">
            {
              chartData.reduce(
                (max: any, item: any) =>
                  max.revenuePerClinic > item.revenuePerClinic ? max : item,
                chartData[0]
              ).segment
            }
          </p>
        </div>
      </div>
    </div>
  )
}

export default ClinicRevenueChart
