import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample data for clinic counts by segment
const clinicCountData = [
  { segment: "Primary Care", clinics: 42, growth: 3, target: 45 },
  { segment: "Specialty Care", clinics: 28, growth: 5, target: 32 },
  { segment: "Urgent Care", clinics: 15, growth: 2, target: 18 },
  { segment: "Pediatrics", clinics: 12, growth: 1, target: 15 },
  { segment: "Women's Health", clinics: 8, growth: 2, target: 10 },
  { segment: "Mental Health", clinics: 6, growth: 3, target: 10 },
  { segment: "Rehabilitation", clinics: 5, growth: 0, target: 8 },
]

const ClinicCountChart = ({
  data = clinicCountData,
}: {
  data?: typeof clinicCountData
}) => {
  // Ensure data is an array
  const chartData = Array.isArray(data) ? data : clinicCountData

  // Calculate total clinics and growth
  const totalClinics = chartData.reduce(
    (sum: number, item: any) => sum + item.clinics,
    0
  )
  const totalTarget = chartData.reduce(
    (sum: number, item: any) => sum + item.target,
    0
  )
  const totalGrowth = chartData.reduce(
    (sum: number, item: any) => sum + item.growth,
    0
  )
  const growthPercent = Math.round(
    (totalGrowth / (totalClinics - totalGrowth)) * 100
  )

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <div className="rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
            Total: {totalClinics}
          </div>
          <div className="rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
            Growth: +{totalGrowth} ({growthPercent}%)
          </div>
        </div>
      </div>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <BarChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="segment" tick={{ fontSize: 12 }} />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Bar dataKey="clinics" name="Current Clinics" fill="#8884d8" />
          <Bar dataKey="target" name="Target" fill="#82ca9d" />
        </BarChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-2">
        <div className="rounded bg-blue-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">Total Clinics</p>
          <p className="text-lg font-bold text-blue-600">{totalClinics}</p>
        </div>
        <div className="rounded bg-green-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">YoY Growth</p>
          <p className="text-lg font-bold text-green-600">
            +{totalGrowth} ({growthPercent}%)
          </p>
        </div>
        <div className="rounded bg-purple-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">Gap to Target</p>
          <p className="text-lg font-bold text-purple-600">
            {totalTarget - totalClinics}
          </p>
        </div>
      </div>
    </div>
  )
}

export default ClinicCountChart
