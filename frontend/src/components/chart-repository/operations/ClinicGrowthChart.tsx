import React from "react"
import {
  <PERSON><PERSON>ian<PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample data for clinic growth over time
const clinicGrowthData = [
  {
    year: "2018",
    primaryCare: 28,
    specialtyCare: 18,
    urgentCare: 8,
    total: 54,
  },
  {
    year: "2019",
    primaryCare: 32,
    specialtyCare: 20,
    urgentCare: 10,
    total: 62,
  },
  {
    year: "2020",
    primaryCare: 35,
    specialtyCare: 22,
    urgentCare: 12,
    total: 69,
  },
  {
    year: "2021",
    primaryCare: 38,
    specialtyCare: 24,
    urgentCare: 13,
    total: 75,
  },
  {
    year: "2022",
    primaryCare: 40,
    specialtyCare: 26,
    urgentCare: 14,
    total: 80,
  },
  {
    year: "2023",
    primaryCare: 42,
    specialtyCare: 28,
    urgentCare: 15,
    total: 85,
  },
  {
    year: "2024",
    primaryCare: 45,
    specialtyCare: 30,
    urgentCare: 17,
    total: 92,
  },
  {
    year: "2025",
    primaryCare: 48,
    specialtyCare: 32,
    urgentCare: 20,
    total: 100,
    projected: true,
  },
]

const ClinicGrowthChart = ({
  data = clinicGrowthData,
}: {
  data?: typeof clinicGrowthData
}) => {
  // Ensure data is an array
  const chartData = Array.isArray(data) ? data : clinicGrowthData

  // Calculate growth metrics
  const currentYear = new Date().getFullYear().toString()
  const currentYearData =
    chartData.find((item: { year: string }) => item.year === currentYear) ||
    chartData[chartData.length - 2]
  const previousYearData =
    chartData.find(
      (item: { year: string }) =>
        item.year === (parseInt(currentYear) - 1).toString()
    ) || chartData[chartData.length - 3]

  const totalGrowth = currentYearData.total - previousYearData.total
  const growthPercent = Math.round((totalGrowth / previousYearData.total) * 100)

  const cagr = Math.round(
    (Math.pow(
      chartData[chartData.length - 2].total / chartData[0].total,
      1 / (chartData.length - 2)
    ) -
      1) *
      100
  )

  // Find the year with highest growth rate
  let highestGrowthYear = ""
  let highestGrowthRate = 0

  for (let i = 1; i < chartData.length - 1; i++) {
    const yearGrowth = chartData[i].total - chartData[i - 1].total
    const yearGrowthRate = yearGrowth / chartData[i - 1].total

    if (yearGrowthRate > highestGrowthRate) {
      highestGrowthRate = yearGrowthRate
      highestGrowthYear = chartData[i].year
    }
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <div className="rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
            Current Total: {currentYearData.total}
          </div>
          <div className="rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
            YoY Growth: +{totalGrowth} ({growthPercent}%)
          </div>
        </div>
      </div>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <LineChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="year" tick={{ fontSize: 12 }} />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Line
            type="monotone"
            dataKey="total"
            name="Total Clinics"
            stroke="#8884d8"
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="primaryCare"
            name="Primary Care"
            stroke="#82ca9d"
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="specialtyCare"
            name="Specialty Care"
            stroke="#ffc658"
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="urgentCare"
            name="Urgent Care"
            stroke="#ff7300"
            strokeWidth={2}
          />
          <ReferenceLine
            x={currentYear}
            stroke="red"
            strokeDasharray="3 3"
            label={{
              value: "Current",
              position: "insideBottomRight",
              fontSize: 12,
            }}
          />
        </LineChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-2">
        <div className="rounded bg-blue-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">YoY Growth</p>
          <p className="text-lg font-bold text-blue-600">
            +{totalGrowth} ({growthPercent}%)
          </p>
        </div>
        <div className="rounded bg-green-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">CAGR</p>
          <p className="text-lg font-bold text-green-600">{cagr}%</p>
        </div>
        <div className="rounded bg-purple-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">Highest Growth</p>
          <p className="text-lg font-bold text-purple-600">
            {highestGrowthYear}
          </p>
        </div>
      </div>
    </div>
  )
}

export default ClinicGrowthChart
