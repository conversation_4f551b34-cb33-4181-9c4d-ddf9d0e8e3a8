import React from "react"
import {
  Bar,
  CartesianGrid,
  Cell,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON>A<PERSON>s,
  <PERSON>A<PERSON><PERSON>,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample data for clinic operational efficiency metrics
const clinicEfficiencyData = [
  {
    clinic: "Downtown Medical",
    patientsPerDay: 85,
    utilizationRate: 92,
    waitTime: 18,
    staffRatio: 4.2,
    costPerPatient: 124,
  },
  {
    clinic: "Westside Health",
    patientsPerDay: 72,
    utilizationRate: 88,
    waitTime: 22,
    staffRatio: 3.8,
    costPerPatient: 135,
  },
  {
    clinic: "North County Care",
    patientsPerDay: 65,
    utilizationRate: 82,
    waitTime: 25,
    staffRatio: 3.5,
    costPerPatient: 142,
  },
  {
    clinic: "Eastside Family",
    patientsPerDay: 78,
    utilizationRate: 90,
    waitTime: 20,
    staffRatio: 4.0,
    costPerPatient: 130,
  },
  {
    clinic: "Central Urgent",
    patientsPerDay: 95,
    utilizationRate: 96,
    waitTime: 15,
    staffRatio: 4.5,
    costPerPatient: 118,
  },
  {
    clinic: "Harbor View",
    patientsPerDay: 68,
    utilizationRate: 84,
    waitTime: 24,
    staffRatio: 3.6,
    costPerPatient: 138,
  },
  {
    clinic: "Southside Medical",
    patientsPerDay: 70,
    utilizationRate: 86,
    waitTime: 23,
    staffRatio: 3.7,
    costPerPatient: 136,
  },
]

const ClinicEfficiencyChart = ({
  data = clinicEfficiencyData,
}: {
  data?: typeof clinicEfficiencyData
}) => {
  // Ensure data is an array
  const chartData = Array.isArray(data) ? data : clinicEfficiencyData

  // Calculate averages
  const avgPatientsPerDay = Math.round(
    chartData.reduce((sum: number, item: any) => sum + item.patientsPerDay, 0) /
      chartData.length
  )

  const avgUtilizationRate = Math.round(
    chartData.reduce(
      (sum: number, item: any) => sum + item.utilizationRate,
      0
    ) / chartData.length
  )

  const avgWaitTime = Math.round(
    chartData.reduce((sum: number, item: any) => sum + item.waitTime, 0) /
      chartData.length
  )

  const avgCostPerPatient = Math.round(
    chartData.reduce((sum: number, item: any) => sum + item.costPerPatient, 0) /
      chartData.length
  )

  // Sort data by patients per day (efficiency)
  const sortedData = [...chartData].sort(
    (a, b) => b.patientsPerDay - a.patientsPerDay
  )

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <div className="rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
            Avg Patients/Day: {avgPatientsPerDay}
          </div>
          <div className="rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
            Avg Utilization: {avgUtilizationRate}%
          </div>
        </div>
      </div>

      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <ComposedChart data={sortedData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="clinic" tick={{ fontSize: 12 }} />
          <YAxis
            yAxisId="left"
            orientation="left"
            domain={[0, "dataMax + 10"]}
            tick={{ fontSize: 12 }}
          />
          <YAxis
            yAxisId="right"
            orientation="right"
            domain={[0, 100]}
            tick={{ fontSize: 12 }}
          />
          <Tooltip />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Bar
            dataKey="patientsPerDay"
            name="Patients Per Day"
            yAxisId="left"
            fill="#8884d8"
          />
          <Line
            type="monotone"
            dataKey="utilizationRate"
            name="Utilization Rate (%)"
            stroke="#82ca9d"
            yAxisId="right"
            strokeWidth={2}
          />
          <Line
            type="monotone"
            dataKey="waitTime"
            name="Wait Time (min)"
            stroke="#ff7300"
            yAxisId="left"
            strokeWidth={2}
          />
        </ComposedChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-4 gap-2">
        <div className="rounded bg-blue-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">Avg Patients/Day</p>
          <p className="text-lg font-bold text-blue-600">{avgPatientsPerDay}</p>
        </div>
        <div className="rounded bg-green-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">Utilization Rate</p>
          <p className="text-lg font-bold text-green-600">
            {avgUtilizationRate}%
          </p>
        </div>
        <div className="rounded bg-yellow-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">Avg Wait Time</p>
          <p className="text-lg font-bold text-yellow-600">{avgWaitTime} min</p>
        </div>
        <div className="rounded bg-purple-50 p-2 text-center">
          <p className="text-xs font-medium text-gray-500">Cost Per Patient</p>
          <p className="text-lg font-bold text-purple-600">
            ${avgCostPerPatient}
          </p>
        </div>
      </div>
    </div>
  )
}

export default ClinicEfficiencyChart
