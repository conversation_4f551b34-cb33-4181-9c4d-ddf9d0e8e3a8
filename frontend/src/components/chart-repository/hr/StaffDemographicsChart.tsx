import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample age distribution by occupation
const sampleAgeDistribution = [
  // Doctor age distribution (typically older, more experienced)
  { occupation: "Doctor", age: 28, count: 3 },
  { occupation: "Doctor", age: 31, count: 8 },
  { occupation: "Doctor", age: 34, count: 12 },
  { occupation: "Doctor", age: 37, count: 15 },
  { occupation: "Doctor", age: 40, count: 18 },
  { occupation: "Doctor", age: 43, count: 16 },
  { occupation: "Doctor", age: 46, count: 12 },
  { occupation: "Doctor", age: 49, count: 10 },
  { occupation: "Doctor", age: 52, count: 8 },
  { occupation: "Doctor", age: 55, count: 6 },
  { occupation: "Doctor", age: 58, count: 4 },
  { occupation: "Doctor", age: 61, count: 2 },

  // Clinic Assistant age distribution (younger, entry-level)
  { occupation: "Clinic Assistant", age: 20, count: 5 },
  { occupation: "Clinic Assistant", age: 23, count: 12 },
  { occupation: "Clinic Assistant", age: 26, count: 18 },
  { occupation: "Clinic Assistant", age: 29, count: 22 },
  { occupation: "Clinic Assistant", age: 32, count: 20 },
  { occupation: "Clinic Assistant", age: 35, count: 16 },
  { occupation: "Clinic Assistant", age: 38, count: 12 },
  { occupation: "Clinic Assistant", age: 41, count: 8 },
  { occupation: "Clinic Assistant", age: 44, count: 6 },
  { occupation: "Clinic Assistant", age: 47, count: 4 },
  { occupation: "Clinic Assistant", age: 50, count: 2 },

  // Staff Nurse age distribution (mid-range)
  { occupation: "Staff Nurse", age: 22, count: 4 },
  { occupation: "Staff Nurse", age: 25, count: 8 },
  { occupation: "Staff Nurse", age: 28, count: 14 },
  { occupation: "Staff Nurse", age: 31, count: 18 },
  { occupation: "Staff Nurse", age: 34, count: 20 },
  { occupation: "Staff Nurse", age: 37, count: 18 },
  { occupation: "Staff Nurse", age: 40, count: 15 },
  { occupation: "Staff Nurse", age: 43, count: 12 },
  { occupation: "Staff Nurse", age: 46, count: 9 },
  { occupation: "Staff Nurse", age: 49, count: 6 },
  { occupation: "Staff Nurse", age: 52, count: 4 },
  { occupation: "Staff Nurse", age: 55, count: 2 },

  // Radiographer age distribution
  { occupation: "Radiographer", age: 24, count: 2 },
  { occupation: "Radiographer", age: 27, count: 4 },
  { occupation: "Radiographer", age: 30, count: 6 },
  { occupation: "Radiographer", age: 33, count: 8 },
  { occupation: "Radiographer", age: 36, count: 7 },
  { occupation: "Radiographer", age: 39, count: 5 },
  { occupation: "Radiographer", age: 42, count: 3 },
  { occupation: "Radiographer", age: 45, count: 2 },

  // Patient Relations Consultant age distribution
  { occupation: "Patient Relations Consultant", age: 23, count: 3 },
  { occupation: "Patient Relations Consultant", age: 26, count: 5 },
  { occupation: "Patient Relations Consultant", age: 29, count: 7 },
  { occupation: "Patient Relations Consultant", age: 32, count: 6 },
  { occupation: "Patient Relations Consultant", age: 35, count: 4 },
  { occupation: "Patient Relations Consultant", age: 38, count: 3 },

  // Dental Assistant age distribution
  { occupation: "Dental Assistant", age: 21, count: 3 },
  { occupation: "Dental Assistant", age: 24, count: 5 },
  { occupation: "Dental Assistant", age: 27, count: 6 },
  { occupation: "Dental Assistant", age: 30, count: 4 },
  { occupation: "Dental Assistant", age: 33, count: 3 },
  { occupation: "Dental Assistant", age: 36, count: 1 },
]

// Sample staff count by occupation
const sampleStaffByOccupation = [
  { occupation: "Doctor", count: 104 },
  { occupation: "Clinic Assistant", count: 125 },
  { occupation: "Staff Nurse", count: 115 },
  { occupation: "Radiographer", count: 35 },
  { occupation: "Patient Relations Consultant", count: 28 },
  { occupation: "Dental Assistant", count: 22 },
  { occupation: "Executive", count: 18 },
  { occupation: "Manager", count: 15 },
  { occupation: "Sonographer", count: 12 },
  { occupation: "Healthcare Assistant", count: 10 },
]

// Create chart data by transforming the age distribution into age groups
const createChartData = () => {
  // Define age groups
  const ageGroups = [
    { range: "20-24", min: 20, max: 24 },
    { range: "25-29", min: 25, max: 29 },
    { range: "30-34", min: 30, max: 34 },
    { range: "35-39", min: 35, max: 39 },
    { range: "40-44", min: 40, max: 44 },
    { range: "45-49", min: 45, max: 49 },
    { range: "50-54", min: 50, max: 54 },
    { range: "55-59", min: 55, max: 59 },
    { range: "60+", min: 60, max: 100 },
  ]

  // Get top 6 occupations for better visualization
  const topOccupations = [
    "Doctor",
    "Clinic Assistant",
    "Staff Nurse",
    "Radiographer",
    "Patient Relations Consultant",
    "Dental Assistant",
  ]

  const chartData = ageGroups.map((ageGroup) => {
    const dataPoint: any = { ageGroup: ageGroup.range }

    topOccupations.forEach((occupation) => {
      const occupationData = sampleAgeDistribution.filter(
        (item) =>
          item.occupation === occupation &&
          item.age >= ageGroup.min &&
          item.age <= ageGroup.max
      )

      const totalCount = occupationData.reduce(
        (sum, item) => sum + item.count,
        0
      )
      dataPoint[occupation] = totalCount
    })

    return dataPoint
  })

  return chartData
}

const chartData = createChartData()

interface StaffDemographicsChartProps {
  data?: typeof chartData
}

const StaffDemographicsChart: React.FC<StaffDemographicsChartProps> = ({
  data = chartData,
}) => {
  // Calculate overall metrics
  const totalStaff = sampleStaffByOccupation.reduce(
    (sum, item) => sum + item.count,
    0
  )
  const topSixOccupations = [
    "Doctor",
    "Clinic Assistant",
    "Staff Nurse",
    "Radiographer",
    "Patient Relations Consultant",
    "Dental Assistant",
  ]

  const topSixCount = topSixOccupations.reduce((sum, occupation) => {
    const count =
      sampleStaffByOccupation.find((item) => item.occupation === occupation)
        ?.count || 0
    return sum + count
  }, 0)

  const topSixPercent = Math.round((topSixCount / totalStaff) * 100)

  // Find most common age group
  const ageGroupCounts = data.map((group) => ({
    ageGroup: group.ageGroup,
    total: topSixOccupations.reduce((sum, occ) => sum + (group[occ] || 0), 0),
  }))
  const mostCommonAgeGroup = ageGroupCounts.reduce((max, current) =>
    current.total > max.total ? current : max
  )

  return (
    <div className="flex flex-col gap-4">
      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <ComposedChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="ageGroup" tick={{ fontSize: 12 }} />
          <YAxis
            label={{
              value: "Number of Staff",
              angle: -90,
              position: "insideLeft",
              fontSize: 12,
            }}
            tick={{ fontSize: 12 }}
          />
          <Tooltip
            formatter={(value, name) => [value, name]}
            labelFormatter={(label) => `Age Group: ${label}`}
          />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Bar dataKey="Doctor" name="Doctor" stackId="staff" fill="#8884d8" />
          <Bar
            dataKey="Clinic Assistant"
            name="Clinic Assistant"
            stackId="staff"
            fill="#82ca9d"
          />
          <Bar
            dataKey="Staff Nurse"
            name="Staff Nurse"
            stackId="staff"
            fill="#ff7c7c"
          />
          <Bar
            dataKey="Radiographer"
            name="Radiographer"
            stackId="staff"
            fill="#ffc658"
          />
          <Bar
            dataKey="Patient Relations Consultant"
            name="Patient Relations Consultant"
            stackId="staff"
            fill="#8dd1e1"
          />
          <Bar
            dataKey="Dental Assistant"
            name="Dental Assistant"
            stackId="staff"
            fill="#d084d0"
          />
        </ComposedChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-2 text-center">
        <div className="rounded bg-blue-50 p-2">
          <p className="text-xs font-medium text-gray-500">Total Staff</p>
          <p className="text-lg font-bold text-blue-600">{totalStaff}</p>
        </div>
        <div className="rounded bg-green-50 p-2">
          <p className="text-xs font-medium text-gray-500">Top 6 Roles</p>
          <p className="text-lg font-bold text-green-600">
            {topSixPercent}% of staff
          </p>
        </div>
        <div className="rounded bg-purple-50 p-2">
          <p className="text-xs font-medium text-gray-500">Peak Age Group</p>
          <p className="text-lg font-bold text-purple-600">
            {mostCommonAgeGroup.ageGroup}
          </p>
        </div>
      </div>
    </div>
  )
}

export default StaffDemographicsChart
