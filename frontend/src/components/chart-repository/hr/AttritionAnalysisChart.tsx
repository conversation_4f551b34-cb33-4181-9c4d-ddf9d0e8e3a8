import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON>ltip,
  <PERSON><PERSON>xi<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample attrition data - 3 bars (positive, neutral, negative) each stacked by business segments
const sampleAttritionData = [
  {
    sentiment: "Positive",
    aesthetics: 35,
    corporate: 28,
    imaging: 42,
    oncology: 38,
    paediatrics: 31,
    womensHealth: 33,
    others: 17,
    total: 224,
  },
  {
    sentiment: "Neutral",
    aesthetics: 22,
    corporate: 18,
    imaging: 25,
    oncology: 24,
    paediatrics: 19,
    womensHealth: 21,
    others: 21,
    total: 150,
  },
  {
    sentiment: "Negative",
    aesthetics: 18,
    corporate: 15,
    imaging: 20,
    oncology: 19,
    paediatrics: 16,
    womensHealth: 18,
    others: 20,
    total: 126,
  },
]

interface AttritionAnalysisChartProps {
  data?: typeof sampleAttritionData
}

const AttritionAnalysisChart: React.FC<AttritionAnalysisChartProps> = ({
  data = sampleAttritionD<PERSON>,
}) => {
  // Calculate overall metrics from all sentiment data
  const totalPeople = data.reduce((sum, item) => sum + item.total, 0)
  const totalPositive =
    data.find((item) => item.sentiment === "Positive")?.total || 0
  const totalNeutral =
    data.find((item) => item.sentiment === "Neutral")?.total || 0
  const totalNegative =
    data.find((item) => item.sentiment === "Negative")?.total || 0

  const overallMetrics = {
    positivePercentage: ((totalPositive / totalPeople) * 100).toFixed(1),
    neutralPercentage: ((totalNeutral / totalPeople) * 100).toFixed(1),
    negativePercentage: ((totalNegative / totalPeople) * 100).toFixed(1),
    totalPeople,
  }

  return (
    <div className="flex flex-col gap-4">
      <ResponsiveContainer width="99%" height={CHART_HEIGHT * 1.125}>
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="sentiment" tick={{ fontSize: 12 }} />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip
            formatter={(value, name, props) => {
              const currentItem = props.payload
              const itemTotal = currentItem?.total || 1
              const percentage = ((Number(value) / itemTotal) * 100).toFixed(1)
              return [`${value} (${percentage}%)`, name]
            }}
          />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Bar
            dataKey="aesthetics"
            name="Aesthetics"
            stackId="segment"
            fill="#8884d8"
          />
          <Bar
            dataKey="corporate"
            name="Corporate"
            stackId="segment"
            fill="#82ca9d"
          />
          <Bar
            dataKey="imaging"
            name="Imaging"
            stackId="segment"
            fill="#ffc658"
          />
          <Bar
            dataKey="oncology"
            name="Oncology"
            stackId="segment"
            fill="#ff7c7c"
          />
          <Bar
            dataKey="paediatrics"
            name="Paediatrics"
            stackId="segment"
            fill="#8dd1e1"
          />
          <Bar
            dataKey="womensHealth"
            name="Women's Health"
            stackId="segment"
            fill="#d084d0"
          />
          <Bar
            dataKey="others"
            name="Others"
            stackId="segment"
            fill="#ffb347"
          />
        </BarChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-2 text-center">
        <div className="rounded bg-green-50 p-2">
          <p className="text-xs font-medium text-gray-500">
            Positive Sentiment
          </p>
          <p className="text-lg font-bold text-green-600">
            {overallMetrics.positivePercentage}%
          </p>
          <p className="text-xs text-gray-500">
            {totalPositive} of {totalPeople} people
          </p>
        </div>
        <div className="rounded bg-yellow-50 p-2">
          <p className="text-xs font-medium text-gray-500">Neutral Sentiment</p>
          <p className="text-lg font-bold text-yellow-600">
            {overallMetrics.neutralPercentage}%
          </p>
          <p className="text-xs text-gray-500">
            {totalNeutral} of {totalPeople} people
          </p>
        </div>
        <div className="rounded bg-red-50 p-2">
          <p className="text-xs font-medium text-gray-500">
            Negative Sentiment
          </p>
          <p className="text-lg font-bold text-red-600">
            {overallMetrics.negativePercentage}%
          </p>
          <p className="text-xs text-gray-500">
            {totalNegative} of {totalPeople} people
          </p>
        </div>
      </div>
    </div>
  )
}

export default AttritionAnalysisChart
