"use client"

import React from "react"

import { Card } from "@/components/chart-repository/Card"
import AttritionAnalysisChart from "@/components/chart-repository/hr/AttritionAnalysisChart"
import EmployeeTurnoverChart from "@/components/chart-repository/hr/EmployeeTurnover<PERSON>hart"
import StaffBenefitsChart from "@/components/chart-repository/hr/StaffBenefitsChart"
import StaffCareerDevelopmentChart from "@/components/chart-repository/hr/StaffCareerDevelopmentChart"
import StaffCompensationChart from "@/components/chart-repository/hr/StaffCompensationChart"
import StaffDemographicsChart from "@/components/chart-repository/hr/StaffDemographicsChart"
import StaffDiversityChart from "@/components/chart-repository/hr/StaffDiversityChart"
import StaffEngagementChart from "@/components/chart-repository/hr/StaffEngagementChart"
import StaffPerformanceChart from "@/components/chart-repository/hr/StaffPerformanceChart"
import StaffRecruitmentChart from "@/components/chart-repository/hr/StaffRecruitmentChart"
import StaffTrainingChart from "@/components/chart-repository/hr/StaffTrainingChart"
import StaffWorkloadChart from "@/components/chart-repository/hr/StaffWorkloadChart"

const CHARTS = [
  {
    id: "HR-01",
    name: "Employee Turnover Analysis",
    component: <EmployeeTurnoverChart />,
  },
  {
    id: "HR-02",
    name: "Staff Compensation Analysis",
    component: <StaffCompensationChart />,
  },
  {
    id: "HR-03",
    name: "Staff Training & Development",
    component: <StaffTrainingChart />,
  },
  {
    id: "HR-04",
    name: "Staff Diversity & Inclusion",
    component: <StaffDiversityChart />,
  },
  {
    id: "HR-05",
    name: "Staff Recruitment & Retention",
    component: <StaffRecruitmentChart />,
  },
  {
    id: "HR-06",
    name: "Staff Engagement & Satisfaction",
    component: <StaffEngagementChart />,
  },
  {
    id: "HR-07",
    name: "Staff Demographics",
    component: <StaffDemographicsChart />,
  },
  {
    id: "HR-08",
    name: "Staff Workload & Productivity",
    component: <StaffWorkloadChart />,
  },
  {
    id: "HR-09",
    name: "Staff Benefits Analysis",
    component: <StaffBenefitsChart />,
  },
  {
    id: "HR-10",
    name: "Staff Performance Evaluation",
    component: <StaffPerformanceChart />,
  },
  {
    id: "HR-11",
    name: "Staff Career Development",
    component: <StaffCareerDevelopmentChart />,
  },
  {
    id: "HR-12",
    name: "Attrition Analysis",
    component: <AttritionAnalysisChart />,
  },
]

const HRCharts = () => (
  <div className="grid gap-4 lg:grid-cols-2">
    {CHARTS.map((chart, index) => (
      <Card key={index} id={chart.id} title={chart.name}>
        {chart.component}
      </Card>
    ))}
  </div>
)

export default HRCharts
