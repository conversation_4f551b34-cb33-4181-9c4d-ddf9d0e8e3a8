import React from "react"
import {
  <PERSON>,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ReferenceLine,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { CHART_HEIGHT } from "@/components/CardComponents"

// Sample workload data by department
const sampleWorkloadByDept = [
  { department: "Nursing", actualFTE: 42, budgetedFTE: 45, workload: 112 },
  { department: "Physicians", actualFTE: 28, budgetedFTE: 30, workload: 105 },
  { department: "Admin", actualFTE: 15, budgetedFTE: 18, workload: 92 },
  { department: "Lab", actualFTE: 12, budgetedFTE: 12, workload: 108 },
  { department: "Radiology", actualFTE: 8, budgetedFTE: 10, workload: 95 },
  { department: "Pharmacy", actualFTE: 6, budgetedFTE: 8, workload: 88 },
]

interface StaffWorkloadChartProps {
  data?: typeof sampleWorkloadByDept
}

const StaffWorkloadChart: React.FC<StaffWorkloadChartProps> = ({
  data = sampleWorkloadByDept,
}) => {
  // Calculate overall metrics
  const totalActualFTE = sampleWorkloadByDept.reduce(
    (sum, item) => sum + item.actualFTE,
    0
  )
  const totalBudgetedFTE = sampleWorkloadByDept.reduce(
    (sum, item) => sum + item.budgetedFTE,
    0
  )
  const fteDifference = totalBudgetedFTE - totalActualFTE
  const fteVariancePercent = Math.round(
    (fteDifference / totalBudgetedFTE) * 100
  )

  const avgWorkload = Math.round(
    sampleWorkloadByDept.reduce((sum, item) => sum + item.workload, 0) /
      sampleWorkloadByDept.length
  )

  return (
    <div className="flex flex-col gap-4">
      <ResponsiveContainer width="99%" height={CHART_HEIGHT}>
        <ComposedChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="department" tick={{ fontSize: 12 }} />
          <YAxis
            yAxisId="left"
            label={{
              value: "FTE",
              angle: -90,
              position: "insideLeft",
              fontSize: 12,
            }}
            tick={{ fontSize: 12 }}
          />
          <YAxis
            yAxisId="right"
            orientation="right"
            domain={[80, 120]}
            label={{
              value: "Workload %",
              angle: 90,
              position: "insideRight",
              fontSize: 12,
            }}
            tick={{ fontSize: 12 }}
          />
          <Tooltip />
          <Legend
            verticalAlign="top"
            wrapperStyle={{ fontSize: 12, paddingBottom: 12 }}
          />
          <Bar
            yAxisId="left"
            dataKey="actualFTE"
            name="Actual FTE"
            fill="#8884d8"
          />
          <Bar
            yAxisId="left"
            dataKey="budgetedFTE"
            name="Budgeted FTE"
            fill="#82ca9d"
          />
          <Line
            yAxisId="right"
            type="monotone"
            dataKey="workload"
            name="Workload %"
            stroke="#ff7300"
            strokeWidth={2}
          />
          <ReferenceLine
            yAxisId="right"
            y={100}
            stroke="red"
            strokeDasharray="3 3"
          />
        </ComposedChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-2 text-center">
        <div className="rounded bg-blue-50 p-2">
          <p className="text-xs font-medium text-gray-500">FTE Variance</p>
          <p className="text-lg font-bold text-blue-600">
            {fteDifference} ({fteVariancePercent}%)
          </p>
          <p className="text-xs text-gray-500">
            {fteDifference > 0 ? "Understaffed" : "Overstaffed"}
          </p>
        </div>
        <div className="rounded bg-green-50 p-2">
          <p className="text-xs font-medium text-gray-500">Avg Workload</p>
          <p className="text-lg font-bold text-green-600">{avgWorkload}%</p>
          <p className="text-xs text-gray-500">
            {avgWorkload > 105 ? "High" : avgWorkload < 95 ? "Low" : "Optimal"}
          </p>
        </div>
        <div className="rounded bg-purple-50 p-2">
          <p className="text-xs font-medium text-gray-500">
            Departments Over 100%
          </p>
          <p className="text-lg font-bold text-purple-600">
            {sampleWorkloadByDept.filter((d) => d.workload > 100).length} of{" "}
            {sampleWorkloadByDept.length}
          </p>
        </div>
      </div>
    </div>
  )
}

export default StaffWorkloadChart
